'use client';
import { useCallback, useEffect, useState, useRef } from 'react';
import useEmblaCarousel from 'embla-carousel-react';
import styles from './style.module.scss';

export default function TestimonialCarousel({ testimonials, locale = 'fr' }) {
  const [emblaRef, emblaApi] = useEmblaCarousel({
    loop: false,
    dragFree: true,
    containScroll: 'trimSnaps'
  });
  const [scrollProgress, setScrollProgress] = useState(0);
  const progressBarRef = useRef(null);

  const onScroll = useCallback((emblaApi) => {
    const progress = Math.max(0, Math.min(1, emblaApi.scrollProgress()));
    setScrollProgress(progress);
  }, []);

  const handleProgressBarHover = useCallback((event) => {
    if (!emblaApi || !progressBarRef.current) return;

    const rect = progressBarRef.current.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const progress = Math.max(0, Math.min(1, x / rect.width));

    // Calculer la position de défilement basée sur le pourcentage
    const scrollTo = progress * (emblaApi.scrollSnapList().length - 1);
    emblaApi.scrollTo(Math.round(scrollTo));
  }, [emblaApi]);

  const handleProgressBarClick = useCallback((event) => {
    handleProgressBarHover(event);
  }, [handleProgressBarHover]);

  useEffect(() => {
    if (!emblaApi) return;

    onScroll(emblaApi);
    emblaApi.on('reInit', onScroll);
    emblaApi.on('scroll', onScroll);
    emblaApi.on('slideFocus', onScroll);
  }, [emblaApi, onScroll]);

  return (
    <div className={styles.carousel}>
      {/* Embla Carousel Container */}
      <div className={styles.embla} ref={emblaRef}>
        <div className={styles.emblaContainer}>
          {testimonials.map((testimonial, index) => (
            <div key={index} className={styles.emblaSlide}>
              <div className={styles.testimonialCard}>
                <blockquote className={styles.quote}>
                  "{testimonial.content}"
                </blockquote>
                <cite className={styles.author}>
                  {testimonial.name}
                </cite>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Indicateur de progression en ligne */}
      <div
        className={styles.progressIndicator}
        ref={progressBarRef}
        onMouseMove={handleProgressBarHover}
        onClick={handleProgressBarClick}
      >
        <div
          className={styles.progressBar}
          style={{ width: `${scrollProgress * 100}%` }}
        />
      </div>
    </div>
  );
}
